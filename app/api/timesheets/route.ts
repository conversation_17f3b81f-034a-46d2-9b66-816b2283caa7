import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/timesheets?startDate=YYYY-MM-DD&endDate=YYYY-MM-DD&employeeId=optional
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const startDateStr = searchParams.get('startDate')
    const endDateStr = searchParams.get('endDate')
    const requestedEmployeeId = searchParams.get('employeeId')

    if (!startDateStr || !endDateStr) {
      return NextResponse.json({ error: 'startDate and endDate are required' }, { status: 400 })
    }

    // Determine employee scope
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { employee: true }
    })
    if (!currentUser?.employee) {
      return NextResponse.json({ error: 'Employee record not found' }, { status: 404 })
    }

    let employeeId = currentUser.employee.id
    if (requestedEmployeeId && ['ADMIN', 'HR', 'MANAGER'].includes(currentUser.role)) {
      employeeId = requestedEmployeeId
    }

    const startDate = new Date(startDateStr)
    const endDate = new Date(endDateStr)

    // Fetch attendance records within range
    const records = await prisma.attendanceRecord.findMany({
      where: {
        employeeId,
        date: {
          gte: new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate()),
          lte: new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate()),
        },
      },
      include: {
        checkInOut: {
          orderBy: { timestamp: 'asc' }
        }
      },
      orderBy: { date: 'asc' }
    })

    // Map to timesheet entries
    const timesheet = records.map((r) => {
      const totalHours = Number(r.workHours || 0)
      return {
        date: r.date,
        checkIn: r.checkIn,
        checkOut: r.checkOut,
        totalHours,
        overtimeHours: Number(r.overtime || 0),
        status: r.status,
        notes: r.notes || '',
        entries: r.checkInOut.map(e => ({ type: e.type, timestamp: e.timestamp }))
      }
    })

    return NextResponse.json({ timesheet })
  } catch (error) {
    console.error('Error fetching timesheet:', error)
    return NextResponse.json({ error: 'Failed to fetch timesheet' }, { status: 500 })
  }
}

// POST /api/timesheets - update notes for a day
// { date: 'YYYY-MM-DD', notes: '...' , employeeId?: string }
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { date, notes, employeeId: requestedEmployeeId } = body || {}
    if (!date) {
      return NextResponse.json({ error: 'date is required' }, { status: 400 })
    }

    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { employee: true }
    })
    if (!currentUser?.employee) {
      return NextResponse.json({ error: 'Employee record not found' }, { status: 404 })
    }

    let employeeId = currentUser.employee.id
    if (requestedEmployeeId && ['ADMIN', 'HR', 'MANAGER'].includes(currentUser.role)) {
      employeeId = requestedEmployeeId
    }

    const day = new Date(date)
    const dateOnly = new Date(day.getFullYear(), day.getMonth(), day.getDate())

    // Ensure attendance record exists
    const attendance = await prisma.attendanceRecord.upsert({
      where: {
        employeeId_date: {
          employeeId,
          date: dateOnly,
        }
      },
      create: {
        employeeId,
        date: dateOnly,
        status: 'PRESENT'
      },
      update: {}
    })

    const updated = await prisma.attendanceRecord.update({
      where: { id: attendance.id },
      data: { notes }
    })

    return NextResponse.json({ success: true, recordId: updated.id })
  } catch (error) {
    console.error('Error updating timesheet:', error)
    return NextResponse.json({ error: 'Failed to update timesheet' }, { status: 500 })
  }
}



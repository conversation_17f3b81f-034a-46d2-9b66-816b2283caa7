'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Calendar, Download } from 'lucide-react'
import { format, startOfWeek, endOfWeek } from 'date-fns'
import { useToast } from '@/hooks/use-toast'

interface TimesheetEntry {
  date: string
  checkIn?: string
  checkOut?: string
  totalHours: number
  overtimeHours: number
  status: string
  notes?: string
  entries: Array<{ type: string; timestamp: string }>
}

export default function TimesheetsPage() {
  const { toast } = useToast()
  const [startDate, setStartDate] = useState(format(startOfWeek(new Date()), 'yyyy-MM-dd'))
  const [endDate, setEndDate] = useState(format(endOfWeek(new Date()), 'yyyy-MM-dd'))
  const [rows, setRows] = useState<TimesheetEntry[]>([])
  const [loading, setLoading] = useState(true)

  const fetchData = async () => {
    try {
      setLoading(true)
      const res = await fetch(`/api/timesheets?startDate=${startDate}&endDate=${endDate}`)
      if (!res.ok) throw new Error('Failed to fetch')
      const data = await res.json()
      const mapped = (data.timesheet || []).map((r: any) => ({
        date: r.date,
        checkIn: r.checkIn,
        checkOut: r.checkOut,
        totalHours: r.totalHours,
        overtimeHours: r.overtimeHours,
        status: r.status,
        notes: r.notes,
        entries: r.entries || []
      }))
      setRows(mapped)
    } catch (e) {
      console.error(e)
      toast({ title: 'Error', description: 'Failed to load timesheet', variant: 'destructive' })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => { fetchData() }, [])

  const downloadCSV = () => {
    const header = 'Date,Check In,Check Out,Total Hours,Overtime,Status,Notes\n'
    const body = rows.map(r => (
      `${format(new Date(r.date), 'yyyy-MM-dd')},${r.checkIn ? format(new Date(r.checkIn), 'HH:mm') : ''},${r.checkOut ? format(new Date(r.checkOut), 'HH:mm') : ''},${r.totalHours},${r.overtimeHours},${r.status},"${(r.notes||'').replace(/"/g,'"')}"`
    )).join('\n')
    const blob = new Blob([header + body], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `timesheet-${startDate}-to-${endDate}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }

  const updateNotes = async (date: string, notes: string) => {
    try {
      const res = await fetch('/api/timesheets', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ date, notes })
      })
      if (!res.ok) throw new Error('Failed to save')
      toast({ title: 'Saved', description: 'Notes updated' })
    } catch (e) {
      toast({ title: 'Error', description: 'Failed to save notes', variant: 'destructive' })
    }
  }

  return (
    <div className="space-y-6 p-4 md:p-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Timesheets</h1>
          <p className="text-muted-foreground">View and export your work hours</p>
        </div>
        <Button onClick={downloadCSV} className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          Download CSV
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Select Range
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="space-y-1">
              <label className="text-sm">Start</label>
              <Input type="date" value={startDate} onChange={(e) => setStartDate(e.target.value)} />
            </div>
            <div className="space-y-1">
              <label className="text-sm">End</label>
              <Input type="date" value={endDate} onChange={(e) => setEndDate(e.target.value)} />
            </div>
            <Button onClick={fetchData}>Apply</Button>
          </div>
        </CardContent>
      </Card>

      <div className="rounded-md border overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Check In</TableHead>
              <TableHead>Check Out</TableHead>
              <TableHead>Total Hours</TableHead>
              <TableHead>Overtime</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Notes</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow><TableCell colSpan={7}>Loading...</TableCell></TableRow>
            ) : rows.length === 0 ? (
              <TableRow><TableCell colSpan={7}>No data</TableCell></TableRow>
            ) : (
              rows.map((r) => (
                <TableRow key={r.date}>
                  <TableCell>{format(new Date(r.date), 'MMM dd, yyyy')}</TableCell>
                  <TableCell>{r.checkIn ? format(new Date(r.checkIn), 'p') : '-'}</TableCell>
                  <TableCell>{r.checkOut ? format(new Date(r.checkOut), 'p') : '-'}</TableCell>
                  <TableCell>{r.totalHours}</TableCell>
                  <TableCell>{r.overtimeHours}</TableCell>
                  <TableCell>{r.status.replace('_',' ')}</TableCell>
                  <TableCell>
                    <Input
                      defaultValue={r.notes}
                      onBlur={(e) => updateNotes(format(new Date(r.date), 'yyyy-MM-dd'), e.target.value)}
                      placeholder="Add notes"
                    />
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}



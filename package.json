{"name": "pekka-hr", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "prisma db seed"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.7.4", "@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.15.0", "@prisma/extension-accelerate": "^2.0.2", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@types/nodemailer": "^7.0.1", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "jspdf": "^3.0.2", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.542.0", "next": "15.5.2", "next-auth": "^5.0.0-beta.29", "nodemailer": "^6.10.1", "puppeteer": "^24.19.0", "react": "19.1.0", "react-day-picker": "^9.9.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.62.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/bcryptjs": "^2.4.6", "@types/jspdf": "^1.3.3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/ui": "^3.2.4", "eslint": "^9", "eslint-config-next": "15.5.2", "jsdom": "^26.1.0", "prisma": "^6.15.0", "tailwindcss": "^4", "tsx": "^4.20.5", "tw-animate-css": "^1.3.7", "typescript": "^5", "vitest": "^3.2.4"}}
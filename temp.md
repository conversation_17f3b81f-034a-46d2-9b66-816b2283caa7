also i need to know and see who approved the leave in leaves module.

i need hr and admin to be able to configure and 
assign an set of available locations with radius for an employee and 
employee can only login when he is that radius .
this should be in employee section .
means locations in employee section should be like
you can add 5 locations max . 
either an custom location or select an office location from the list .

other wise it should go to an diffrent section 
to the manager/hr(if manager not defined) for attendence req approval . user should be able to see if he is in defined location or not .

if there is dummy data anywhere fix it .

create an good 404 page.

every action must be logged like hr123 created this leave type, 
emp123 viewed this information ,
and there should be an logs section 
where it should be visible to admin only .
logs View page missing in admin also add it side bar with an option to print it 

onBoarding tasks/ complete onboarding flow is not working properly and is not customisazble ,
there is broken flow fix and complete the module .


i also need time sheet module to be added . like normal hrms 

recheck work on payrolls and salary . if everything is proper we need it to be working flawlessly i also need payslips and reports properly .

also we need to add mails to the reporting manager /hr / employee . whenever neccary acc to actions
eg:- consider if i request a leave i should get mail leave request sent . manager should get mail new mail req.... .
when its accepted leave request accepted . ...blah blah blah and soo on .


restructure the dashboard according to below
dashbord should have 
1 your info Card
1 holidays/upcoming leaves Card
1 monthly attendence Preview Card (like the image attached should be there in both desktop and mobile)
1 Check In Check Out Card
1 Leave Report Card
1 anouncements Card
1 On leave today Card
1 Pending Tasks Card (if any)

also i need reports download button whereever neccasary .

i can't create salary comopnenet . and salary structure component is incomplete all together ig so complete it fully then 
currently salary structure and employee and salary doesn't have any relation .

also where there is an options and there could be many options like employees there can be 1000's of employee to select from options instead of select use command select component in which user can search as well so user can directly search via it .